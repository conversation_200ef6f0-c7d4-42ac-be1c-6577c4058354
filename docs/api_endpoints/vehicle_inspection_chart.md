# Vehicle Inspection Chart API

## GET /v1/inspections/charts/numb-vehicle-inspected-asset-vs-digispect

This endpoint returns chart data showing the number of vehicles inspected grouped by inspection frequency, comparing asset vehicles (registered vehicles) vs digispect vehicles (external vehicles).

### Query Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| start_datetime | string (ISO 8601) | Yes | Start date and time for the data range |
| end_datetime | string (ISO 8601) | Yes | End date and time for the data range |
| is_from_digispect | boolean | No | Filter for digispect-only data (default: false) |

### Example Request

```
GET /v1/inspections/charts/numb-vehicle-inspected-asset-vs-digispect?start_datetime=2024-01-01T00:00:00Z&end_datetime=2024-12-31T23:59:59Z
Authorization: Bearer <token>
```

### Response Format

```json
{
  "success": true,
  "message": "Success",
  "reference_id": "",
  "data": [
    {
      "name": "Never",
      "code": "NEVER",
      "num_asset_vehicles": 10,
      "num_digispect_vehicles": 10
    },
    {
      "name": "1",
      "code": "ONE",
      "num_asset_vehicles": 16,
      "num_digispect_vehicles": 14
    },
    {
      "name": "2",
      "code": "TWO",
      "num_asset_vehicles": 20,
      "num_digispect_vehicles": 5
    },
    {
      "name": "3-5",
      "code": "THREE_TO_FIVE",
      "num_asset_vehicles": 10,
      "num_digispect_vehicles": 16
    },
    {
      "name": ">5",
      "code": "MORE_THAN_FIVE",
      "num_asset_vehicles": 10,
      "num_digispect_vehicles": 14
    }
  ]
}
```

### Response Fields

| Field | Type | Description |
|-------|------|-------------|
| name | string | Display name for the inspection frequency category |
| code | string | Internal code for the frequency category |
| num_asset_vehicles | float64 | Number of asset vehicles (registered vehicles) in this frequency category |
| num_digispect_vehicles | float64 | Number of digispect vehicles (external vehicles) in this frequency category |

### Frequency Categories

- **Never**: Vehicles that have never been inspected
- **1**: Vehicles inspected exactly once
- **2**: Vehicles inspected exactly twice  
- **3-5**: Vehicles inspected 3 to 5 times
- **>5**: Vehicles inspected more than 5 times

### Chart Visualization

This data is designed to be displayed as a grouped bar chart with:
- X-axis: Inspection frequency categories (Never, 1, 2, 3-5, >5)
- Y-axis: Number of vehicles
- Two bar series:
  - Blue bars: Registered Vehicles (num_asset_vehicles)
  - Orange bars: External Vehicles (num_digispect_vehicles)

### Authentication

Requires a valid bearer token in the Authorization header.

### Error Responses

- `400 Bad Request`: Invalid query parameters
- `401 Unauthorized`: Missing or invalid authentication token
- `500 Internal Server Error`: Server error occurred
