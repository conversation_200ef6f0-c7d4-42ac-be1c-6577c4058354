package repository

import (
	"assetfindr/internal/app/asset/models"
	"assetfindr/internal/infrastructure/database"
	"assetfindr/pkg/common/commonmodel"
	"context"
)

type AssetInspectionRepository interface {
	CreateAssetInspection(ctx context.Context, dB database.DBI, assetInspection *models.AssetInspection) error
	GetAssetInspectionList(ctx context.Context, dB database.DBI, param models.GetAssetInspectionListParam) (int, []models.AssetInspection, error)
	UpdateAssetInspection(ctx context.Context, dB database.DBI, id string, assetInspection *models.AssetInspection) error
	GetAssetInspection(ctx context.Context, dB database.DBI, cond models.AssetInspectionCondition) (*models.AssetInspection, error)
	GetAssetInspections(ctx context.Context, dB database.DBI, cond models.AssetInspectionCondition) ([]models.AssetInspection, error)

	UpsertAssetInspectionAssignment(ctx context.Context, dB database.DBI, ticketContact *models.AssetInspectionAssignment) error
	GetAssetInspectionAssignments(ctx context.Context, dB database.DBI, condition models.AssetInspectionAssignmentCondition) ([]models.AssetInspectionAssignment, error)
	GetAssetInspectionByAssignedUserId(ctx context.Context, dB database.DBI, userId string, referenceCode string) ([]models.AssetInspection, error)

	DoneInspectionByReferenceID(ctx context.Context, dB database.DBI, referenceID string) error
	ChartCountSingleAndLinkedInspections(ctx context.Context, dB database.DBI, req models.InspectionChartReq) ([]commonmodel.Chart, error)
	ChartVehicleInspectionFrequency(ctx context.Context, dB database.DBI, req models.InspectionChartReq) ([]commonmodel.Chart, error)
	ChartInspectionLocations(ctx context.Context, dB database.DBI, req models.InspectionChartReq) ([]models.InspectionLocationChart, error)
	ChartInspectionsByInspectorPerDate(ctx context.Context, dB database.DBI, req models.InspectionChartReq) ([]commonmodel.Chart, error)
	ChartTotalInspections(ctx context.Context, dB database.DBI, req models.InspectionChartReq) ([]commonmodel.Chart, error)
	ChartTotalLinkedTyresInspections(ctx context.Context, dB database.DBI, req models.InspectionChartReq) ([]commonmodel.Chart, error)
	ChartTotalCustomers(ctx context.Context, dB database.DBI, req models.InspectionChartReq) ([]commonmodel.Chart, error)
	ChartTotalVehiclesInspected(ctx context.Context, dB database.DBI, req models.InspectionChartReq) ([]commonmodel.Chart, error)
	ChartTotalTyresInspected(ctx context.Context, dB database.DBI, req models.InspectionChartReq) ([]commonmodel.Chart, error)
	ChartTotalInspectors(ctx context.Context, dB database.DBI, req models.InspectionChartReq) ([]commonmodel.Chart, error)
	ChartTop5InspectedCustomers(ctx context.Context, dB database.DBI, req models.InspectionChartReq) ([]commonmodel.Chart, error)
	ChartCustomersByInspectionCount(ctx context.Context, dB database.DBI, req models.InspectionChartReq) ([]commonmodel.Chart, error)
}

type AssetInspectionVehicleRepository interface {
	GetAssetInspectionVehicleList(ctx context.Context, dB database.DBI, param models.GetAssetInspectionVehicleListParam) (int, []models.AssetInspectionVehicle, error)
	GetAssetInspectionVehicle(ctx context.Context, dB database.DBI, cond models.AssetInspectionVehicleCondition) (*models.AssetInspectionVehicle, error)
	CreateAssetInspectionVehicle(ctx context.Context, dB database.DBI, assetInspectionVehicle *models.AssetInspectionVehicle) error
	UpdateAssetInspectionVehicle(ctx context.Context, dB database.DBI, id string, assetInspectionVehicle *models.AssetInspectionVehicle) error
	GetAssetInspectionVehiclesByIds(ctx context.Context, dB database.DBI, vehicles *[]models.AssetInspectionVehicle, ids []string) error
	GetLatestAssetInspectionVehicle(ctx context.Context, dB database.DBI, cond models.AssetInspectionVehicleCondition) (*models.AssetInspectionVehicle, error)
	ChartVehicleInspectionPerDate(ctx context.Context, dB database.DBI, req models.InspectionChartReq) ([]commonmodel.Chart, error)
	ChartAssetVehicleAndCustomerInspectionsPerDate(ctx context.Context, dB database.DBI, req models.GetChartAssetVehicleCustomerInspectionPerDateReq) ([]models.ChartAssetVehicleCustomerInspectionPerDate, error)
	ChartTop5VehicleBrands(ctx context.Context, dB database.DBI, req models.InspectionChartReq) ([]commonmodel.Chart, error)
	ChartNumbersOfVehicleInspectedAssetVsDigispect(ctx context.Context, dB database.DBI, req models.ChartNumbersOfVehicleInspectedAssetVsDigispectReq) ([]models.ChartNumbersOfVehicleInspectedAssetVsDigispect, error)
}

type AssetInspectionTyreRepository interface {
	GetAssetInspectionTyres(ctx context.Context, dB database.DBI, tyres *[]models.AssetInspectionTyre, pagination commonmodel.ListRequest, inspectionID string) (int, error)
	GetAssetInspectionTyreList(ctx context.Context, dB database.DBI, param models.GetAssetInspectionTyreListParam) (int, []models.AssetInspectionTyre, error)
	GetAssetInspectionTyreListExport(ctx context.Context, dB database.DBI, param models.GetAssetInspectionTyreListParam) (int, []models.AssetInspectionTyre, error)
	CreateAssetInspectionTyre(ctx context.Context, dB database.DBI, assetInspectionTyre *models.AssetInspectionTyre) error
	UpdateAssetInspectionTyre(ctx context.Context, dB database.DBI, id string, assetInspectionTyre *models.AssetInspectionTyre) error
	GetAssetInspectionTyre(ctx context.Context, dB database.DBI, cond models.AssetInspectionTyreCondition) (*models.AssetInspectionTyre, error)
	GetAssetInspectionTyresV2(ctx context.Context, dB database.DBI, cond models.AssetInspectionTyreCondition) ([]models.AssetInspectionTyre, error)

	ChartTotalInspectionToDate(ctx context.Context, dB database.DBI, clientID string) ([]commonmodel.Chart, error)
	ChartCountInspectionTyreType(ctx context.Context, dB database.DBI, clientID string) ([]commonmodel.Chart, error)
	ChartCountInstalledTyreInspected(ctx context.Context, dB database.DBI, clientID string) ([]commonmodel.Chart, error)
	ChartCountInstalledTyreNotInspected(ctx context.Context, dB database.DBI, clientID string) ([]commonmodel.Chart, error)
	ChartTyreInspectionPerDate(ctx context.Context, dB database.DBI, req models.InspectionChartReq) ([]commonmodel.Chart, error)
	ChartTop5TyreBrandBySize(ctx context.Context, dB database.DBI, req models.InspectionChartReq) ([]commonmodel.Chart, error)
}
