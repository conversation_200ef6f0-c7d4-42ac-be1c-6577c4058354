package models

import (
	"assetfindr/pkg/common/commonmodel"
	"time"

	"github.com/jackc/pgtype"
	"gopkg.in/guregu/null.v4"
)

type AssetInspectionVehicle struct {
	commonmodel.Model
	AssetInspectionID string          `json:"asset_inspection_id" gorm:"type:varchar(40)"`
	AssetInspection   AssetInspection `json:"asset_inspection"`
	AssetVehicleID    string          `json:"asset_vehicle_id" gorm:"default:null"`
	AssetVehicle      AssetVehicle    `json:"asset_vehicle" gorm:"foreignKey:AssetVehicleID"`
	Remark            string          `json:"remark" gorm:"type:varchar(255)"`
	AssetAssignmentID string          `json:"asset_assignment_id" gorm:"type:varchar(40);not null"`
	VehicleKM         float64         `json:"vehicle_km" gorm:"default:0"`
	VehicleHm         int             `json:"vehicle_hm" gorm:"default:0"`
	ClientID          string          `json:"client_id" gorm:"type:varchar(40);not null"`

	RequireSpooringVehicle null.Bool `json:"require_spooring_vehicle" gorm:"default:null"`
	FailedVisualChecking   null.Bool `json:"failed_visual_checking" gorm:"default:null"`
	RequireRotationTyre    null.Bool `json:"require_rotation_tyre" gorm:"default:null"`
	TireTreadAndRimDamage  null.Bool `json:"tire_tread_and_rim_damage" gorm:"default:null"`

	MaxRtdDiffTolerance   *null.Int    `gorm:"default:null" json:"max_rtd_diff_tolerance" pgjsonb:"max_rtd_diff_tolerance"`
	AxleConfiguration     pgtype.JSONB `json:"axle_configuration" gorm:"default:null"`
	CustomBrandName       null.String  `json:"custom_brand_name" gorm:"default:null"`
	CustomModelName       null.String  `json:"custom_model_name" gorm:"default:null"`
	CustomReferenceNumber null.String  `json:"custom_reference_number" gorm:"default:null"`
	CustomSerialNumber    null.String  `json:"custom_serial_number" gorm:"default:null"`

	DeviceID       string      `json:"device_id" gorm:"default:null"`
	SourceTypeCode null.String `json:"source_type_code" gorm:"default:null"`

	DigispectVehicleID string   `json:"digispect_vehicle_id" gorm:"default:null"`
	PartnerOwnerName   string   `json:"partner_owner_name" gorm:"default:null"`
	PartnerOwnerID     string   `json:"partner_owner_id" gorm:"default:null"`
	NumberOfTyres      null.Int `json:"number_of_tyres" gorm:"default:null"`
	NumberOfSpareTyres null.Int `json:"number_of_spare_tyres" gorm:"default:null"`

	SourceType AssetInspectionSourceTypes `json:"source_type" gorm:"foreignKey:SourceTypeCode;references:code"`

	Asset                Asset                 `json:"asset" gorm:"foreignKey:AssetVehicleID"`
	AssetInspectionTyres []AssetInspectionTyre `json:"asset_inspection_tyres" gorm:"foreignKey:AssetInspectionID;references:AssetInspectionID"`

	DigispectConfigID string `gorm:"default:null"`
	DigispectConfig   DigispectConfig
	DigispectVehicle  *DigispectVehicle `gorm:"foreignKey:DigispectVehicleID;references:ID"`
}

func (a *AssetInspectionVehicle) TableName() string {
	return "ams_asset_inspection_vehicle"
}

func (a *AssetInspectionVehicle) SetID() {
	a.SetUUID("aiv")
}

type AssetInspectionVehicleWhere struct {
	ClientID          string
	InspectionID      string
	AssetID           string
	ReferenceID       string
	ReferenceCode     string
	StartDate         string
	EndDate           string
	AssetIDs          []string
	InspectByUserIDs  []string
	HasPartnerOwnerID bool
	DigispectConfIDs  []string
}

type AssetInspectionVehiclePreload struct {
	AssetInspection          bool
	AssetVehicle             bool
	Asset                    bool
	AssetLocation            bool
	AssetBrand               bool
	AssetVehicleAsset        bool
	AssetVehicleVehicle      bool
	AssetInspectionTyres     bool
	AssetVehicleVehicleBrand bool

	AssetInspectionTyresAssetTyre bool
	DigispectVehicle              bool
}

type AssetInspectionVehicleCondition struct {
	Where   AssetInspectionVehicleWhere
	Preload AssetInspectionVehiclePreload
	Columns []string
}

type GetAssetInspectionVehicleListParam struct {
	commonmodel.ListRequest
	Cond AssetInspectionVehicleCondition
}

type GetChartAssetVehicleCustomerInspectionPerDateReq struct {
	ClientID       string
	StartDatetime  time.Time
	EndDatetime    time.Time
	PartnerOwnerID string
}

type ChartNumbersOfVehicleInspectedAssetVsDigispectReq struct {
	ClientID       string
	StartDatetime  time.Time
	EndDatetime    time.Time
	PartnerOwnerID string
}

type ChartAssetVehicleCustomerInspectionPerDate struct {
	Date           string `json:"date"`
	NumInspections int    `json:"num_inspections"`
	NumCustomers   int    `json:"num_customers"`
}

type ChartNumbersOfVehicleInspectedAssetVsDigispect struct {
	Name                 string  `json:"name"`
	Code                 string  `json:"code"`
	NumAssetVehicles     float64 `json:"num_asset_vehicles"`
	NumDigispectVehicles float64 `json:"num_digispect_vehicles"`
}
